use crate::app::App;
use crate::llm_client;
use crate::reedline_validator::REEDLINE_LAST_LINE;
use reedline::{Prompt, PromptEditMode, PromptHistorySearch, PromptHistorySearchStatus};
use std::borrow::Cow;
use std::sync::{Arc, Mutex};
use tiktoken_rs::{cl100k_base, CoreBPE};

#[derive(Default, Clone)]
pub struct CurrentPromptBufferState {
    pub current_buffer_content: String,
}

#[derive(Default)]
pub struct TokenCache {
    pub system_prompt_tokens: usize,
    pub history_tokens: usize,
    pub history_version: usize, // Track when history changes
}

#[derive(Clone)]
pub struct TokenCountingPrompt {
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    tokenizer: Arc<CoreBPE>,
    token_cache: Arc<Mutex<TokenCache>>,
}

impl TokenCountingPrompt {
    pub fn update_buffer_content(&self, content: String) {
        if let Ok(mut buffer_state) = self.prompt_buffer_state.lock() {
            buffer_state.current_buffer_content = content;
        }
    }

    pub fn get_token_count_for_buffer(&self, buffer_content: &str) -> usize {
        let app_guard = self.app_arc.lock().unwrap();
        let mut cache_guard = self.token_cache.lock().unwrap();

        let current_history_version = app_guard.conversation_history_for_llm.len();

        // Update history tokens cache if conversation history changed
        if cache_guard.history_version != current_history_version {
            cache_guard.history_tokens = app_guard
                .conversation_history_for_llm
                .iter()
                .map(|message| {
                    self.tokenizer
                        .encode_with_special_tokens(&message.content)
                        .len()
                })
                .sum();
            cache_guard.history_version = current_history_version;
        }

        let current_buffer_tokens = self
            .tokenizer
            .encode_with_special_tokens(buffer_content)
            .len();

        cache_guard.system_prompt_tokens
            + cache_guard.history_tokens
            + current_buffer_tokens
    }
}

impl TokenCountingPrompt {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        let tokenizer = Arc::new(cl100k_base().unwrap_or_else(|e| {
            eprintln!(
                "Failed to load cl100k_base tokenizer: {}. Token counting will be inaccurate.",
                e
            );
            panic!("Tokenizer cl100k_base failed to load: {}", e);
        }));

        // Pre-calculate system prompt tokens since it never changes
        let system_prompt_tokens = tokenizer
            .encode_with_special_tokens(llm_client::SYSTEM_PROMPT)
            .len();

        let token_cache = Arc::new(Mutex::new(TokenCache {
            system_prompt_tokens,
            history_tokens: 0,
            history_version: 0,
        }));

        Self {
            app_arc,
            prompt_buffer_state,
            tokenizer,
            token_cache,
        }
    }
}

impl Prompt for TokenCountingPrompt {
    fn render_prompt_left(&self) -> Cow<str> {
        Cow::Owned("".to_string())
    }

    fn render_prompt_right(&self) -> Cow<str> {
        
        let total_tokens = self.get_token_count_for_buffer(REEDLINE_LAST_LINE.lock().unwrap().as_str());
        Cow::Owned(format!("[{} tk]", total_tokens))
    }

    fn render_prompt_indicator(&self, _edit_mode: PromptEditMode) -> Cow<str> {
        Cow::Borrowed("> ")
    }

    fn render_prompt_multiline_indicator(&self) -> Cow<str> {
        Cow::Borrowed("  ")
    }

    fn render_prompt_history_search_indicator(
        &self,
        history_search: PromptHistorySearch,
    ) -> Cow<str> {
        let prefix = match history_search.status {
            PromptHistorySearchStatus::Passing => "",
            PromptHistorySearchStatus::Failing => "failing ",
        };
        Cow::Owned(format!(
            "({}reverse-search: {}) ",
            prefix, history_search.term
        ))
    }

    fn right_prompt_on_last_line(&self) -> bool {
        true
    }
}


