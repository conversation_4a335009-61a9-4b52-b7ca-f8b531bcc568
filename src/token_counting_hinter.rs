use crate::app::App;
use crate::cli_prompt::{CurrentPromptBufferState, TokenCache};
use crate::llm_client;
use nu_ansi_term::{Color, Style};
use reedline::{Hinter, History, SearchQuery};
use std::sync::{Arc, Mutex};
use tiktoken_rs::{cl100k_base, CoreBPE};

/// A hinter that combines history-based hints with real-time token counting
/// and updates the prompt buffer state on every character input
pub struct TokenCountingHinter {
    // History hinting functionality (similar to DefaultHinter)
    style: Style,
    current_hint: String,
    min_chars: usize,
    
    // Token counting functionality
    app_arc: Arc<Mutex<App>>,
    prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    tokenizer: Arc<CoreBPE>,
    token_cache: Arc<Mutex<TokenCache>>,
}

impl TokenCountingHinter {
    pub fn new(
        app_arc: Arc<Mutex<App>>,
        prompt_buffer_state: Arc<Mutex<CurrentPromptBufferState>>,
    ) -> Self {
        let tokenizer = Arc::new(cl100k_base().unwrap_or_else(|e| {
            eprintln!(
                "Failed to load cl100k_base tokenizer: {}. Token counting will be inaccurate.",
                e
            );
            panic!("Tokenizer cl100k_base failed to load: {}", e);
        }));

        // Pre-calculate system prompt tokens since it never changes
        let system_prompt_tokens = tokenizer
            .encode_with_special_tokens(llm_client::SYSTEM_PROMPT)
            .len();

        let token_cache = Arc::new(Mutex::new(TokenCache {
            system_prompt_tokens,
            history_tokens: 0,
            history_version: 0,
        }));

        Self {
            style: Style::new().fg(Color::LightGray),
            current_hint: String::new(),
            min_chars: 1,
            app_arc,
            prompt_buffer_state,
            tokenizer,
            token_cache,
        }
    }

    /// A builder that sets the style applied to the hint as part of the buffer
    #[must_use]
    pub fn with_style(mut self, style: Style) -> Self {
        self.style = style;
        self
    }

    /// A builder that sets the number of characters that have to be present to enable history hints
    #[must_use]
    pub fn with_min_chars(mut self, min_chars: usize) -> Self {
        self.min_chars = min_chars;
        self
    }

    /// Update the prompt buffer state with the current line content
    fn update_buffer_content(&self, content: &str) {
        if let Ok(mut buffer_state) = self.prompt_buffer_state.lock() {
            buffer_state.current_buffer_content = content.to_string();
        }
    }

    /// Get the first token from a string (similar to get_first_token from hinter module)
    fn get_first_token(input: &str) -> String {
        input
            .split_whitespace()
            .next()
            .unwrap_or_default()
            .to_string()
    }
}

impl Hinter for TokenCountingHinter {
    fn handle(
        &mut self,
        line: &str,
        #[allow(unused_variables)] pos: usize,
        history: &dyn History,
        use_ansi_coloring: bool,
    ) -> String {
        // Update the prompt buffer state with the current line content
        // This is the key functionality that makes token counting work in real-time
        self.update_buffer_content(line);

        // Generate history-based hint (similar to DefaultHinter)
        self.current_hint = if line.chars().count() >= self.min_chars {
            // Try to find a history entry that starts with the current line
            if let Ok(search_results) = history.search(SearchQuery::last_with_prefix(
                line.to_string(),
                history.session(),
            )) {
                search_results
                    .first()
                    .map_or_else(String::new, |entry| {
                        entry
                            .command_line
                            .get(line.len()..)
                            .unwrap_or_default()
                            .to_string()
                    })
            } else {
                String::new()
            }
        } else {
            String::new()
        };

        // Return the styled hint if we have one and coloring is enabled
        if use_ansi_coloring && !self.current_hint.is_empty() {
            self.style.paint(&self.current_hint).to_string()
        } else {
            self.current_hint.clone()
        }
    }

    fn complete_hint(&self) -> String {
        self.current_hint.clone()
    }

    fn next_hint_token(&self) -> String {
        Self::get_first_token(&self.current_hint)
    }
}
