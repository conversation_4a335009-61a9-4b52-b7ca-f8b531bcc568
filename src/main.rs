mod app;
mod cli;
mod cli_prompt;
mod confetti;
mod display;
mod hammerspoon;
mod handler;
mod keyboard;
mod llm_client;
mod network;
mod session_manager;
mod suspendable_editor;
mod syntax_highlighting;
mod token_counting_hinter;
mod reedline_validator;

use crate::keyboard::{disable_kitty_keyboard_for_meta_alt_modifiers, enable_kitty_keyboard_for_meta_alt_modifiers};
use crate::suspendable_editor::{ReadResult, SuspendableReedline};
use app::{App, AppMessage, MessageContent};
use crossterm::{
    cursor,
    event::{KeyboardEnhancementFlags, PopKeyboardEnhancementFlags, PushKeyboardEnhancementFlags},
    execute,
    style::Stylize,
    terminal,
};
use reedline::EditCommand;
use std::env::temp_dir;
use std::io::{self, ErrorKind};
use std::process::Command as OsCommand;
use std::sync::{<PERSON>, Mutex};
use std::time::Duration;
use crate::confetti::show_confetti;
use crate::hammerspoon::show_hammerspoon_alert;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let cli_args = cli::parse_cli_args();

    let request_timeout = Duration::from_secs(180);
    let http_client = network::create_client(request_timeout, &cli_args);
    let app_arc = Arc::new(Mutex::new(App::new(http_client.clone())));

    let session_dir_path = session_manager::ensure_session_dir_exists()?;
    let session_id = cli_args
        .restore
        .clone()
        .unwrap_or_else(session_manager::generate_new_session_id);
    let session_file_path = session_manager::get_session_file_path(&session_dir_path, &session_id);

    println!("\nDima AI Agent using grok-3-mini-high via Pollinations.AI");
    println!("Session ID: {}", session_id.clone().yellow());

    if cli_args.restore.is_some() || session_file_path.exists() {
        match session_manager::load_session(&session_file_path) {
            Ok(loaded_llm_history) => {
                if !loaded_llm_history.is_empty() {
                    println!("Restored session: {}", session_id.clone().green());
                    let mut constructed_app_messages = Vec::new();
                    {
                        let mut app_locked = app_arc.lock().unwrap();
                        app_locked.conversation_history_for_llm = loaded_llm_history;
                        for chat_msg in &app_locked.conversation_history_for_llm {
                            let display_msg = if chat_msg.role == "user" {
                                AppMessage {
                                    sender: "User".to_string(),
                                    parts: vec![MessageContent::Text(chat_msg.content.clone())],
                                }
                            } else if chat_msg.role == "assistant" {
                                app_locked.create_ai_app_message_from_raw(&chat_msg.content)
                            } else if chat_msg.role == "tool" {
                                AppMessage {
                                    sender: "Tool Execution".to_string(),
                                    parts: vec![MessageContent::Text(format!(
                                        "[Tool Result]\n{}",
                                        chat_msg.content.clone()
                                    ))],
                                }
                            } else {
                                continue;
                            };
                            constructed_app_messages.push(display_msg);
                        }
                    }
                    if !constructed_app_messages.is_empty() {
                        let mut app_locked = app_arc.lock().unwrap();
                        for msg in &constructed_app_messages {
                            app_locked.messages.push(msg.clone());
                        }
                    }
                    for msg_to_print in &constructed_app_messages {
                        display::print_formatted_message(msg_to_print)?;
                    }
                } else if cli_args.restore.is_some() {
                    println!(
                        "Starting new session (or session file was empty): {}",
                        session_id.clone().yellow()
                    );
                }
            }
            Err(e) if e.kind() == ErrorKind::NotFound => {
                println!(
                    "Starting new session (no existing file found for ID): {}",
                    session_id.clone().yellow()
                );
            }
            Err(e) => {
                eprintln!(
                    "Error loading session '{}': {}. Starting fresh.",
                    session_id.clone().red(),
                    e
                );
            }
        }
    } else {
        println!("Starting new session: {}", session_id.clone().yellow());
    }

    // No need to create history or cursor config separately anymore
    // They're now handled by SuspendableReedline
    let prompt_buffer_state_arc =
        Arc::new(Mutex::new(cli_prompt::CurrentPromptBufferState::default()));
    let token_counting_prompt =
        cli_prompt::TokenCountingPrompt::new(app_arc.clone(), prompt_buffer_state_arc.clone());
    // Create a suspendable editor with external editor support
    let temp_file = temp_dir().join("rust_llm_tui_edit_buffer.tmp");
    let editor_cmd_str = std::env::var("EDITOR").unwrap_or_else(|_| {
       "emacsclient".to_string()
    });
    let mut editor_os_cmd = OsCommand::new(editor_cmd_str);
    editor_os_cmd.arg(&temp_file);

    let mut line_editor =
        SuspendableReedline::create().with_buffer_editor(editor_os_cmd, temp_file);

    enable_kitty_keyboard_for_meta_alt_modifiers();

    'main_loop: loop {
        // Update reedline prompt buffer state before reading line
        {
            let current_buffer = line_editor.current_buffer_contents();
            let mut buffer_state_locked = prompt_buffer_state_arc.lock().unwrap();
            buffer_state_locked.current_buffer_content = current_buffer;
        }

        let read_result = line_editor.read_line(&token_counting_prompt);

        match read_result {
            Ok(ReadResult::Success(buffer)) => {
                let trimmed_buffer = buffer.trim();
                show_hammerspoon_alert(trimmed_buffer);
                if trimmed_buffer == "/exit" {
                    break 'main_loop;
                } else if trimmed_buffer == "/clear" {
                    let messages_to_reprint;
                    {
                        // Lock scope
                        let app_locked = app_arc.lock().unwrap();
                        messages_to_reprint = app_locked.messages.clone();
                    } // Lock released
                    execute!(
                        io::stdout(),
                        terminal::Clear(terminal::ClearType::All),
                        cursor::MoveTo(0, 0)
                    )?;
                    println!("Session ID: {}", session_id.clone().yellow());
                    for msg in messages_to_reprint {
                        display::print_formatted_message(&msg)?;
                    }
                    continue;
                } else if trimmed_buffer == "/help" {
                    println!("\nCtrl-D to quit. Ctrl-C to clear prompt or exit if empty. Cmd+Enter for newline.\n");
                    continue;
                } else if trimmed_buffer.is_empty() {
                    continue;
                }
                handler::process_user_input(
                    buffer,
                    app_arc.clone(),
                    &http_client,
                    &session_file_path,
                )
                .await?;
            }
            Ok(ReadResult::ShouldQuit) => {
                app_arc.lock().unwrap().should_quit = true;
            }
            Ok(ReadResult::ClearBuffer) => {
                line_editor.editor.run_edit_commands(&[EditCommand::Clear]);
            }
            Err(err) => {
                eprintln!("SuspendableReedline error: {:?}. Exiting.", err);
                app_arc.lock().unwrap().should_quit = true;
            }
        }

        if app_arc.lock().unwrap().should_quit {
            break 'main_loop;
        }
    }

    disable_kitty_keyboard_for_meta_alt_modifiers();
    Ok(())
}
