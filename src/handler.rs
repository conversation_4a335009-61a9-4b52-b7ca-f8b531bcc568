use crate::{
    app::{App, AppMessage, MessageContent},
    display, llm_client, session_manager,
};
use crossterm::style::Stylize; // For .yellow(), .bold() etc.
use reqwest::Client;
use std::{
    fs,
    io::{self, Write},
    path::Path,
    sync::{Arc, Mutex},
    time::{Duration, SystemTime},
};
use tokio::process::Command as TokioCommand;
use tokio::time::timeout;

pub async fn process_user_input(
    buffer: String,
    app_arc: Arc<Mutex<App>>,
    http_client: &Client,
    session_file_path: &Path,
) -> Result<(), Box<dyn std::error::Error>> {
    // 1. Create user AppMessage, display it, add to app.messages, add to llm_history
    let user_app_message = AppMessage {
        sender: "User".to_string(),
        parts: vec![MessageContent::Text(buffer.clone())],
    };
    display::print_formatted_message(&user_app_message)?;

    {
        // Lock scope for app update
        let mut app_locked = app_arc.lock().unwrap();
        app_locked.messages.push(user_app_message.clone());
        app_locked.add_user_message_to_llm_history(buffer.clone());
        if let Err(e) =
            session_manager::save_session(session_file_path, &app_locked.conversation_history_for_llm)
        {
            eprintln!("{}", format!("Error saving session: {}", e).red());
        }
    } // Lock released

    let mut max_tool_iterations = 5; // Prevent infinite tool call loops

    'tool_loop: loop {
        if max_tool_iterations == 0 {
            let err_msg_text = "Max tool iterations reached. Aborting sequence.".to_string();
            let err_msg = AppMessage {
                sender: "System".to_string(),
                parts: vec![MessageContent::Text(err_msg_text.clone())],
            };
            display::print_formatted_message(&err_msg)?;
            {
                // Lock scope
                let mut app_locked = app_arc.lock().unwrap();
                app_locked.messages.push(err_msg);
                // Optionally add this system error to LLM history if it should know
                // app_locked.add_tool_response_to_llm_history("system_error", err_msg_text);
            } // Lock released
            break 'tool_loop;
        }
        max_tool_iterations -= 1;

        let messages_for_api: Vec<llm_client::ChatMessage>;
        {
            // Lock scope for reading history
            let app_locked = app_arc.lock().unwrap();
            let mut constructed_messages = vec![llm_client::ChatMessage {
                role: "system".to_string(),
                content: llm_client::SYSTEM_PROMPT.to_string(),
            }];
            constructed_messages.extend_from_slice(&app_locked.conversation_history_for_llm);
            messages_for_api = constructed_messages;
        } // Lock released

        println!("{}", "AI is thinking...".italic().yellow());
        io::stdout().flush()?; // Ensure "thinking" message is displayed

        // Capture the start time for potential retry messages
        let llm_result = llm_client::call_llm_api(http_client, messages_for_api).await;

        match llm_result {
            Ok(response_content) => {
                let ai_app_message_for_display: AppMessage;
                let mut should_continue_tool_loop = false;
                {
                    // Scope for app_arc lock
                    let mut app_locked = app_arc.lock().unwrap();
                    // app_locked.llm_is_thinking = false; // Not strictly needed for CLI if "thinking" is just printed
                    app_locked.add_assistant_response_to_llm_history(response_content.clone());
                    ai_app_message_for_display =
                        app_locked.create_ai_app_message_from_raw(&response_content);
                    app_locked.messages.push(ai_app_message_for_display.clone());

                    if let Some(MessageContent::ToolCall(tool_call_action)) =
                        ai_app_message_for_display.parts.first()
                    {
                        // --- Tool Call Execution Logic ---
                        match tool_call_action.tool_name.as_str() {
                            "read_file" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
                                let offset = args.get("offset").and_then(|v| v.as_i64()).unwrap_or(0);
                                let length = args.get("length").and_then(|v| v.as_i64());

                                if let Some(file_path) = path_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = read_file_with_pagination(&file_path, offset, length);

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output)],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for read_file tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "read_multiple_files" => {
                                let args = &tool_call_action.arguments;
                                let paths_opt = args.get("paths").and_then(|v| v.as_array());

                                if let Some(paths_array) = paths_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let mut results = Vec::new();
                                    for path_value in paths_array {
                                        if let Some(path_str) = path_value.as_str() {
                                            let content = read_file_with_pagination(path_str, 0, None);
                                            results.push(format!("=== {} ===\n{}", path_str, content));
                                        }
                                    }
                                    let tool_output_str = results.join("\n\n");

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output)],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for read_multiple_files tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "write_file" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
                                let content_opt = args.get("content").and_then(|v| v.as_str()).map(String::from);
                                let mode = args.get("mode").and_then(|v| v.as_str()).unwrap_or("rewrite");

                                if let (Some(file_path), Some(content)) = (path_opt, content_opt) {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = write_file_with_mode(&file_path, &content, mode);

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for write_file tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "create_directory" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);

                                if let Some(dir_path) = path_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = match fs::create_dir_all(&dir_path) {
                                        Ok(_) => {
                                            format!("Directory '{}' created successfully.", dir_path)
                                        }
                                        Err(e) => {
                                            format!("Failed to create directory '{}': {}", dir_path, e)
                                        }
                                    };

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for create_directory tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "list_directory" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
                                let recursive = args.get("recursive").and_then(|v| v.as_bool()).unwrap_or(false);

                                if let Some(dir_path) = path_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = if recursive {
                                        list_directory_detailed_recursive(&dir_path)
                                    } else {
                                        list_directory_detailed(&dir_path)
                                    };

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for list_directory tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "move_file" => {
                                let args = &tool_call_action.arguments;
                                let source_opt = args.get("source").and_then(|v| v.as_str()).map(String::from);
                                let dest_opt = args.get("destination").and_then(|v| v.as_str()).map(String::from);

                                if let (Some(source), Some(destination)) = (source_opt, dest_opt) {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = match fs::rename(&source, &destination) {
                                        Ok(_) => {
                                            format!("Successfully moved '{}' to '{}'", source, destination)
                                        }
                                        Err(e) => {
                                            format!("Failed to move '{}' to '{}': {}", source, destination, e)
                                        }
                                    };

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for move_file tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "search_files" => {
                                let args = &tool_call_action.arguments;
                                let pattern_opt = args.get("pattern").and_then(|v| v.as_str()).map(String::from);
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from).unwrap_or_else(|| ".".to_string());

                                if let Some(pattern) = pattern_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = search_files_by_name(&pattern, &path_opt);

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for search_files tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "search_code" => {
                                let args = &tool_call_action.arguments;
                                let pattern_opt = args.get("pattern").and_then(|v| v.as_str()).map(String::from);
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from).unwrap_or_else(|| ".".to_string());
                                let file_pattern_opt = args.get("file_pattern").and_then(|v| v.as_str()).map(String::from);

                                if let Some(pattern) = pattern_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = search_code_content(&pattern, &path_opt, file_pattern_opt.as_deref());

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for search_code tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "get_file_info" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);

                                if let Some(file_path) = path_opt {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = get_file_metadata(&file_path);

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for get_file_info tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "edit_block" => {
                                let args = &tool_call_action.arguments;
                                let path_opt = args.get("path").and_then(|v| v.as_str()).map(String::from);
                                let old_text_opt = args.get("old_text").and_then(|v| v.as_str()).map(String::from);
                                let new_text_opt = args.get("new_text").and_then(|v| v.as_str()).map(String::from);

                                if let (Some(file_path), Some(old_text), Some(new_text)) = (path_opt, old_text_opt, new_text_opt) {
                                    drop(app_locked); // Release lock before file operations

                                    display::print_formatted_message(&ai_app_message_for_display)?;

                                    let tool_output_str = edit_text_block(&file_path, &old_text, &new_text);

                                    let truncated_output = display::truncate_tool_output(&tool_output_str);
                                    let tool_result_display_msg = AppMessage {
                                        sender: "Tool Execution".to_string(),
                                        parts: vec![MessageContent::Text(truncated_output.clone())],
                                    };
                                    display::print_formatted_message(&tool_result_display_msg)?;

                                    // Re-acquire lock to update history
                                    let mut app_locked_again = app_arc.lock().unwrap();
                                    app_locked_again.messages.push(tool_result_display_msg);
                                    app_locked_again.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        tool_output_str,
                                    );
                                    should_continue_tool_loop = true;
                                } else {
                                    let err_text = "System: AI provided malformed arguments for edit_block tool.".to_string();
                                    app_locked.add_tool_response_to_llm_history(
                                        &tool_call_action.tool_name,
                                        err_text.clone(),
                                    );
                                    let err_display_msg = AppMessage {
                                        sender: "System".to_string(),
                                        parts: vec![MessageContent::Text(err_text)],
                                    };
                                    app_locked.messages.push(err_display_msg.clone());
                                    drop(app_locked);
                                    display::print_formatted_message(&err_display_msg)?;
                                    should_continue_tool_loop = true;
                                }
                            }
                            "execute_command" => {
                            let args = &tool_call_action.arguments;
                            let command_str_opt = args
                                .get("command")
                                .and_then(|v| v.as_str())
                                .map(String::from);
                            let cwd_opt = args.get("cwd").and_then(|v| v.as_str()).map(String::from);
                            let timeout_seconds = args
                                .get("timeout_seconds")
                                .and_then(|v| v.as_f64())
                                .unwrap_or(30.0) as u64;

                            if let Some(command_str) = command_str_opt {
                                drop(app_locked); // Release lock before blocking for confirmation

                                display::print_formatted_message(&ai_app_message_for_display)?;

                                println!(
                                    "{}",
                                    format!("\nAI requests to execute command: `{}`", command_str)
                                        .yellow()
                                        .bold()
                                );
                                if let Some(ref cwd) = cwd_opt {
                                    println!("{}", format!("In directory: `{}`", cwd).yellow());
                                }
                                println!("{}", format!("Timeout: {} seconds", timeout_seconds).yellow());
                                print!("{}", "Confirm execution? (y/N): ".bold());
                                io::stdout().flush()?;

                                let mut confirmation_input = String::new();
                                io::stdin().read_line(&mut confirmation_input)?;

                                let tool_output_str: String;
                                if confirmation_input.trim().eq_ignore_ascii_case("y") {
                                    let mut tokio_cmd = TokioCommand::new("sh");
                                    tokio_cmd.arg("-c").arg(&command_str);
                                    if let Some(cwd) = cwd_opt {
                                        tokio_cmd.current_dir(cwd);
                                    }

                                    let timeout_duration = Duration::from_secs(timeout_seconds);
                                    match timeout(timeout_duration, tokio_cmd.output()).await {
                                        Ok(Ok(output_val)) => {
                                            let stdout =
                                                String::from_utf8_lossy(&output_val.stdout);
                                            let stderr =
                                                String::from_utf8_lossy(&output_val.stderr);
                                            tool_output_str = format!(
                                                "Command '{}' executed.\nStatus: {}\nSTDOUT:\n{}\nSTDERR:\n{}",
                                                command_str, output_val.status, stdout, stderr
                                            );
                                        }
                                        Ok(Err(e)) => {
                                            tool_output_str = format!(
                                                "Failed to execute command '{}': {}",
                                                command_str, e
                                            );
                                        }
                                        Err(_) => {
                                            tool_output_str = format!(
                                                "Command '{}' timed out after {} seconds",
                                                command_str, timeout_seconds
                                            );
                                        }
                                    }
                                } else {
                                    tool_output_str = format!(
                                        "User denied execution of command: {}",
                                        command_str
                                    );
                                }

                                let truncated_output = display::truncate_tool_output(&tool_output_str);
                                let tool_result_display_msg = AppMessage {
                                    sender: "Tool Execution".to_string(),
                                    parts: vec![MessageContent::Text(truncated_output.clone())],
                                };
                                display::print_formatted_message(&tool_result_display_msg)?;

                                // Re-acquire lock to update history
                                let mut app_locked_again = app_arc.lock().unwrap();
                                app_locked_again.messages.push(tool_result_display_msg);
                                app_locked_again.add_tool_response_to_llm_history(
                                    &tool_call_action.tool_name,
                                    tool_output_str,
                                );
                                should_continue_tool_loop = true;
                            } else {
                                // Malformed arguments for execute_command
                                let err_text = "System: AI provided malformed arguments for execute_command tool.".to_string();
                                app_locked.add_tool_response_to_llm_history(
                                    &tool_call_action.tool_name,
                                    err_text.clone(),
                                );
                                let err_display_msg = AppMessage {
                                    sender: "System".to_string(),
                                    parts: vec![MessageContent::Text(err_text)],
                                };
                                app_locked.messages.push(err_display_msg.clone());
                                drop(app_locked); // Release lock
                                display::print_formatted_message(&err_display_msg)?;
                                should_continue_tool_loop = true;
                            }
                            }
                            _ => {
                                // Handle unknown tools
                                let tool_not_impl_text = format!(
                                    "Tool '{}' is not implemented in this version.",
                                    tool_call_action.tool_name
                                );
                                let tool_not_impl_msg = AppMessage {
                                    sender: "System".to_string(),
                                    parts: vec![MessageContent::Text(tool_not_impl_text.clone())],
                                };
                                app_locked.messages.push(tool_not_impl_msg.clone());
                                app_locked.add_tool_response_to_llm_history(
                                    &tool_call_action.tool_name,
                                    tool_not_impl_text,
                                );
                                drop(app_locked); // Release lock
                                display::print_formatted_message(&ai_app_message_for_display)?; // Show the AI's request
                                display::print_formatted_message(&tool_not_impl_msg)?; // Show the system message
                                should_continue_tool_loop = true; // Let LLM know about this
                            }
                        }
                    } else {
                         drop(app_locked); // Release lock if no tool call processing path took it
                    }
                } // Lock released here if not already manually dropped (e.g. after add_assistant_response...)

                if !should_continue_tool_loop && ai_app_message_for_display.sender == "AI" {
                     let is_execute_command_req = ai_app_message_for_display.parts.first()
                        .is_some_and(|part| matches!(part, MessageContent::ToolCall(tc) if tc.tool_name == "execute_command"));

                    if !is_execute_command_req { // Avoid double printing execute_command request
                        display::print_formatted_message(&ai_app_message_for_display)?;
                    }
                }

                { // Scope for final save in this iteration of tool_loop
                    let app_locked = app_arc.lock().unwrap();
                    if let Err(e) = session_manager::save_session(
                        session_file_path,
                        &app_locked.conversation_history_for_llm,
                    ) {
                        eprintln!("{}", format!("Error saving session: {}", e).red());
                    }
                } // Lock released

                if should_continue_tool_loop {
                    continue 'tool_loop;
                } else {
                    break 'tool_loop;
                }
            }
            Err(e) => {
                let system_error_msg = AppMessage {
                    sender: "System".to_string(),
                    parts: vec![MessageContent::Text(format!("Error from LLM: {}", e))],
                };
                display::print_formatted_message(&system_error_msg)?;
                { // Lock scope
                    app_arc.lock().unwrap().messages.push(system_error_msg);
                } // Lock released
                break 'tool_loop; // Break on LLM error
            }
        }
    } // End 'tool_loop

    Ok(())
}

// Helper function to read file with pagination support
fn read_file_with_pagination(file_path: &str, offset: i64, length: Option<i64>) -> String {
    match fs::read_to_string(file_path) {
        Ok(content) => {
            let lines: Vec<&str> = content.lines().collect();
            let total_lines = lines.len() as i64;

            if total_lines == 0 {
                return format!("File '{}' is empty.", file_path);
            }

            // Calculate start index
            let start_idx = if offset >= 0 {
                offset.min(total_lines - 1) as usize
            } else {
                ((total_lines + offset).max(0)) as usize
            };

            // Calculate end index
            let end_idx = if let Some(len) = length {
                (start_idx as i64 + len).min(total_lines) as usize
            } else {
                total_lines as usize
            };

            let selected_lines = &lines[start_idx..end_idx];
            let result_content = selected_lines.join("\n");

            format!(
                "File '{}' (lines {}-{} of {}):\n{}",
                file_path,
                start_idx + 1,
                end_idx,
                total_lines,
                result_content
            )
        }
        Err(e) => {
            format!("Failed to read file '{}': {}", file_path, e)
        }
    }
}

// Helper function to write file with mode support
fn write_file_with_mode(file_path: &str, content: &str, mode: &str) -> String {
    match mode {
        "append" => {
            match std::fs::OpenOptions::new()
                .create(true)
                .append(true)
                .open(file_path)
            {
                Ok(mut file) => {
                    match file.write_all(content.as_bytes()) {
                        Ok(_) => format!("Content appended to file '{}'. {} bytes added.", file_path, content.len()),
                        Err(e) => format!("Failed to append to file '{}': {}", file_path, e),
                    }
                }
                Err(e) => format!("Failed to open file '{}' for appending: {}", file_path, e),
            }
        }
        _ => { // "rewrite" or default
            match fs::write(file_path, content) {
                Ok(_) => format!("File '{}' written successfully. {} bytes written.", file_path, content.len()),
                Err(e) => format!("Failed to write file '{}': {}", file_path, e),
            }
        }
    }
}

// Helper function to list directory contents with detailed info
fn list_directory_detailed(dir_path: &str) -> String {
    match fs::read_dir(dir_path) {
        Ok(entries) => {
            let mut result = format!("Detailed directory listing for '{}':\n", dir_path);
            let mut items = Vec::new();

            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let path = entry.path();
                        let name = path.file_name().unwrap_or_default().to_string_lossy();

                        if let Ok(metadata) = entry.metadata() {
                            let item_type = if metadata.is_dir() { "DIR " } else { "FILE" };
                            let size = if metadata.is_file() {
                                format!("{:>10}", metadata.len())
                            } else {
                                "         -".to_string()
                            };

                            let modified = metadata.modified()
                                .ok()
                                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                                .map(|duration| {
                                    let secs = duration.as_secs();
                                    chrono::DateTime::from_timestamp(secs as i64, 0)
                                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                                        .unwrap_or_else(|| "Unknown".to_string())
                                })
                                .unwrap_or_else(|| "Unknown".to_string());

                            items.push(format!("{} {} {} {}", item_type, size, modified, name));
                        } else {
                            items.push(format!("ERROR reading metadata for {}", name));
                        }
                    }
                    Err(e) => {
                        items.push(format!("ERROR: {}", e));
                    }
                }
            }

            items.sort();
            for item in items {
                result.push_str(&format!("{}\n", item));
            }
            result
        }
        Err(e) => {
            format!("Failed to list directory '{}': {}", dir_path, e)
        }
    }
}

// Helper function to list directory contents (recursive with details)
fn list_directory_detailed_recursive(dir_path: &str) -> String {
    fn walk_dir(path: &Path, prefix: &str, result: &mut String) {
        match fs::read_dir(path) {
            Ok(entries) => {
                let mut items: Vec<_> = entries.collect();
                items.sort_by_key(|entry| {
                    entry.as_ref().map(|e| e.path()).unwrap_or_default()
                });

                for entry in items {
                    match entry {
                        Ok(entry) => {
                            let entry_path = entry.path();
                            let name = entry_path.file_name().unwrap_or_default().to_string_lossy();

                            if let Ok(metadata) = entry.metadata() {
                                let item_type = if metadata.is_dir() { "DIR " } else { "FILE" };
                                let size = if metadata.is_file() {
                                    format!("{:>8}", metadata.len())
                                } else {
                                    "       -".to_string()
                                };

                                result.push_str(&format!("{}{} {} {}\n", prefix, item_type, size, name));

                                if metadata.is_dir() {
                                    let new_prefix = format!("{}  ", prefix);
                                    walk_dir(&entry_path, &new_prefix, result);
                                }
                            } else {
                                result.push_str(&format!("{}ERROR reading metadata for {}\n", prefix, name));
                            }
                        }
                        Err(e) => {
                            result.push_str(&format!("{}ERROR: {}\n", prefix, e));
                        }
                    }
                }
            }
            Err(e) => {
                result.push_str(&format!("{}ERROR reading directory: {}\n", prefix, e));
            }
        }
    }

    let mut result = format!("Recursive detailed directory listing for '{}':\n", dir_path);
    walk_dir(Path::new(dir_path), "", &mut result);
    result
}

// Helper function to search files by name
fn search_files_by_name(pattern: &str, search_path: &str) -> String {
    let mut results = Vec::new();
    let pattern_lower = pattern.to_lowercase();

    fn search_recursive(dir: &Path, pattern: &str, results: &mut Vec<String>) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                let name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("")
                    .to_lowercase();

                if name.contains(pattern) {
                    results.push(path.to_string_lossy().to_string());
                }

                if path.is_dir() {
                    search_recursive(&path, pattern, results);
                }
            }
        }
    }

    search_recursive(Path::new(search_path), &pattern_lower, &mut results);

    if results.is_empty() {
        format!("No files found matching pattern '{}' in '{}'", pattern, search_path)
    } else {
        results.sort();
        format!("Found {} files matching pattern '{}':\n{}",
                results.len(), pattern, results.join("\n"))
    }
}

// Helper function to search code content
fn search_code_content(pattern: &str, search_path: &str, file_pattern: Option<&str>) -> String {
    let mut results = Vec::new();

    fn search_in_file(file_path: &Path, pattern: &str, results: &mut Vec<String>) {
        if let Ok(content) = fs::read_to_string(file_path) {
            for (line_num, line) in content.lines().enumerate() {
                if line.to_lowercase().contains(&pattern.to_lowercase()) {
                    results.push(format!("{}:{}: {}",
                                       file_path.display(),
                                       line_num + 1,
                                       line.trim()));
                }
            }
        }
    }

    fn search_recursive(dir: &Path, pattern: &str, file_pattern: Option<&str>, results: &mut Vec<String>) {
        if let Ok(entries) = fs::read_dir(dir) {
            for entry in entries.flatten() {
                let path = entry.path();

                if path.is_file() {
                    let should_search = if let Some(fp) = file_pattern {
                        if fp.starts_with("*.") {
                            let ext = &fp[2..];
                            path.extension()
                                .and_then(|e| e.to_str())
                                .map(|e| e == ext)
                                .unwrap_or(false)
                        } else {
                            path.file_name()
                                .and_then(|n| n.to_str())
                                .map(|n| n.contains(fp))
                                .unwrap_or(false)
                        }
                    } else {
                        true
                    };

                    if should_search {
                        search_in_file(&path, pattern, results);
                    }
                } else if path.is_dir() {
                    search_recursive(&path, pattern, file_pattern, results);
                }
            }
        }
    }

    search_recursive(Path::new(search_path), pattern, file_pattern, &mut results);

    if results.is_empty() {
        format!("No matches found for pattern '{}' in '{}'", pattern, search_path)
    } else {
        format!("Found {} matches for pattern '{}':\n{}",
                results.len(), pattern, results.join("\n"))
    }
}

// Helper function to get file metadata
fn get_file_metadata(file_path: &str) -> String {
    match fs::metadata(file_path) {
        Ok(metadata) => {
            let file_type = if metadata.is_file() {
                "File"
            } else if metadata.is_dir() {
                "Directory"
            } else {
                "Other"
            };

            let size = metadata.len();
            let readonly = metadata.permissions().readonly();

            let created = metadata.created()
                .ok()
                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                .map(|duration| {
                    let secs = duration.as_secs();
                    chrono::DateTime::from_timestamp(secs as i64, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "Unknown".to_string())
                })
                .unwrap_or_else(|| "Unknown".to_string());

            let modified = metadata.modified()
                .ok()
                .and_then(|time| time.duration_since(SystemTime::UNIX_EPOCH).ok())
                .map(|duration| {
                    let secs = duration.as_secs();
                    chrono::DateTime::from_timestamp(secs as i64, 0)
                        .map(|dt| dt.format("%Y-%m-%d %H:%M:%S").to_string())
                        .unwrap_or_else(|| "Unknown".to_string())
                })
                .unwrap_or_else(|| "Unknown".to_string());

            format!(
                "File information for '{}':\nType: {}\nSize: {} bytes\nReadonly: {}\nCreated: {}\nModified: {}",
                file_path, file_type, size, readonly, created, modified
            )
        }
        Err(e) => {
            format!("Failed to get metadata for '{}': {}", file_path, e)
        }
    }
}

// Helper function to edit text blocks
fn edit_text_block(file_path: &str, old_text: &str, new_text: &str) -> String {
    match fs::read_to_string(file_path) {
        Ok(content) => {
            if content.contains(old_text) {
                let new_content = content.replace(old_text, new_text);
                match fs::write(file_path, &new_content) {
                    Ok(_) => {
                        let old_lines = old_text.lines().count();
                        let new_lines = new_text.lines().count();
                        format!(
                            "Successfully edited '{}'\nReplaced {} lines with {} lines\nOld text: {}\nNew text: {}",
                            file_path,
                            old_lines,
                            new_lines,
                            if old_text.len() > 100 { format!("{}...", &old_text[..100]) } else { old_text.to_string() },
                            if new_text.len() > 100 { format!("{}...", &new_text[..100]) } else { new_text.to_string() }
                        )
                    }
                    Err(e) => format!("Failed to write changes to '{}': {}", file_path, e),
                }
            } else {
                format!("Text block not found in file '{}'. No changes made.", file_path)
            }
        }
        Err(e) => {
            format!("Failed to read file '{}': {}", file_path, e)
        }
    }
}
